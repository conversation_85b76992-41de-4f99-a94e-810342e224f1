<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Helpers\JewelHelper;

class JewelServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('jewel', function () {
            return new JewelHelper();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Helper functions are loaded via composer autoload
    }
}
